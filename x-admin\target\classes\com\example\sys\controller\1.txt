package com.example.sys.controller;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.commom.vo.Result;
import com.example.sys.annotation.OperationLog;
import com.example.sys.entity.SysProtect;
import com.example.sys.service.ISysProtectService;
import com.example.sys.vo.SysProtectExcelVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-25
 */
@RestController
@RequestMapping("/sysProtect")
public class SysProtectController {
    @Autowired
    private ISysProtectService sysProtectService;

    @GetMapping("/list")
    public Result<Map<String, Object>> getSysProtectList(
            @RequestParam(value = "systemOwnerOrg", required = false) String systemOwnerOrg,
            @RequestParam(value = "memberUnit", required = false) String memberUnit,
            @RequestParam(value = "systemName", required = false) String systemName,
            @RequestParam(value = "systemShortName", required = false) String systemShortName,
            @RequestParam(value = "networkBelonging", required = false) String networkBelonging,
            @RequestParam(value = "businessType", required = false) String businessType,
            @RequestParam(value = "filingNumber", required = false) String filingNumber,
            @RequestParam(value = "classificationLevel", required = false) String classificationLevel,
            @RequestParam(value = "evaluationTime", required = false) String evaluationTime,
            @RequestParam(value = "evaluationResult", required = false) String evaluationResult,
            @RequestParam(value = "plannedEvaluationTime", required = false) String plannedEvaluationTime,
            @RequestParam(value = "evaluationOrganization", required = false) String evaluationOrganization,
            @RequestParam(value = "evaluationStatus", required = false) String evaluationStatus,
            @RequestParam(value = "systemStatus", required = false) String systemStatus,
            @RequestParam(value = "category", required = false) String category,
            @RequestParam(value = "controlPoint", required = false) String controlPoint,
            @RequestParam(value = "evaluationItem", required = false) String evaluationItem,
            @RequestParam(value = "complianceStatus", required = false) String complianceStatus,
            @RequestParam(value = "pageNo") Long pageNo,
            @RequestParam(value = "pageSize") Long pageSize
    ) {
        LambdaQueryWrapper<SysProtect> wrapper = new LambdaQueryWrapper<>();

        if (systemOwnerOrg!= null) {
            wrapper.eq(SysProtect::getSystemOwnerOrg, systemOwnerOrg);
        }
        if (memberUnit != null) {
            wrapper.eq(SysProtect::getMemberUnit, memberUnit);
        }
        if (systemName != null) {
            wrapper.eq(SysProtect::getSystemName, systemName);
        }
        if (systemShortName != null) {
            wrapper.eq(SysProtect::getSystemShortName, systemShortName);
        }
        if (networkBelonging != null) {
            wrapper.eq(SysProtect::getNetworkBelonging, networkBelonging);
        }
        if (businessType != null) {
            wrapper.eq(SysProtect::getBusinessType, businessType);
        }
        if (filingNumber != null) {
            wrapper.eq(SysProtect::getFilingNumber, filingNumber);
        }
        if (classificationLevel != null) {
            wrapper.eq(SysProtect::getClassificationLevel, classificationLevel);
        }
        if (evaluationTime != null) {
            wrapper.eq(SysProtect::getEvaluationTime, evaluationTime);
        }
        if (evaluationResult != null) {
            wrapper.eq(SysProtect::getEvaluationResult, evaluationResult);
        }
        if (plannedEvaluationTime != null) {
            wrapper.eq(SysProtect::getPlannedEvaluationTime, plannedEvaluationTime);
        }
        if (evaluationOrganization != null) {
            wrapper.eq(SysProtect::getEvaluationOrganization, evaluationOrganization);
        }
        if (evaluationStatus != null) {
            wrapper.eq(SysProtect::getEvaluationStatus, evaluationStatus);
        }
        if (systemStatus != null) {
            wrapper.eq(SysProtect::getSystemStatus, systemStatus);
        }
        if (category != null) {
            wrapper.eq(SysProtect::getCategory, category);
        }
        if (controlPoint != null) {
            wrapper.eq(SysProtect::getControlPoint, controlPoint);
        }
        if (evaluationItem != null) {
            wrapper.eq(SysProtect::getEvaluationItem, evaluationItem);
        }
        if (complianceStatus != null) {
            wrapper.eq(SysProtect::getComplianceStatus, complianceStatus);
        }

        Page<SysProtect> page = new Page<>(pageNo, pageSize);//分页页数以及每页显示多少条
        Page<SysProtect> sysProtectPage = sysProtectService.page(page, wrapper);

        Map<String, Object> data = new HashMap<>();
        data.put("sysProtect", sysProtectPage.getRecords());
        data.put("total", sysProtectPage.getTotal());
        data.put("pages", sysProtectPage.getPages());

        return Result.success(data);
    }

    // 新增等保数据
    @PostMapping("/add")
    @OperationLog(
        moduleName = "等保管理",
        operationType = "新增",
        desc = "'新增等保：' + #data.systemName",
        isSensitive = true
    )
    public Result<?> addDept(@RequestBody SysProtect sysProtect) {
        sysProtectService.save(sysProtect);
        return Result.success("新增成功");
    }

    // 删除等保数据
    @DeleteMapping("/{applicationId}") // 建议与主键字段一致
    @OperationLog(
        moduleName = "等保管理",
        operationType = "删除",
        desc = "'删除等保：' + #data.systemName",
        dataId = "#applicationId", // 保持与参数一致
        isSensitive = true
    )
    public Result<?> delete(@PathVariable("applicationId") Integer applicationId) {
        SysProtect sysProtect = sysProtectService.getById(applicationId);
        sysProtectService.removeById(applicationId);
        return Result.success(sysProtect,"删除成功");
    }

    // 修改等保数据
    @PutMapping("/update")
    @OperationLog(
        moduleName = "等保管理",
        operationType = "修改",
        desc = "'修改等保：' + #data.systemName"
    )
    public Result<?> update(@RequestBody SysProtect sysProtect) {
        sysProtectService.updateById(sysProtect);
        return Result.success("修改成功");
    }

    // 查询单个等保数据
    @GetMapping("/{applicationId}") // 建议路径参数名与实体主键一致
    @OperationLog(
        moduleName = "等保管理",
        operationType = "查询",
        desc = "'查询等保：' + #data.systemName"
    )
    public Result<SysProtect> getSysProtectById(@PathVariable("applicationId") Integer applicationId) {
        SysProtect sysProtect = sysProtectService.getById(applicationId);
        return Result.success(sysProtect);
    }

    // 查询所有等保数据
    @GetMapping("/all")
    public Result<List<SysProtect>> getAllDictionary() {
        List<SysProtect> list = sysProtectService.list();
        return Result.success(list);
    }

    @PostMapping("/import")
    @OperationLog(
        moduleName = "等保管理",
        operationType = "导入",
        desc = "导入等保"
    )
    @ResponseBody
    public Result<?> importExcel(@RequestPart("file") MultipartFile file) throws IOException {
        // 1. 读取Excel数据到VO列表
        List<SysProtectExcelVO> voList = EasyExcel.read(file.getInputStream())
            .head(SysProtectExcelVO.class)
            .sheet()
            .doReadSync();
    
        // 2. VO转Entity并添加自动填充逻辑
        List<SysProtect> entityList = voList.stream().map(vo -> {
            SysProtect entity = new SysProtect();
            
            // 手动映射基础字段
            entity.setSystemOwnerOrg(vo.getSystemOwnerOrg());
            entity.setMemberUnit(vo.getMemberUnit());
            entity.setSystemName(vo.getSystemName());
            entity.setSystemShortName(vo.getSystemShortName());
            entity.setNetworkBelonging(vo.getNetworkBelonging());
            entity.setBusinessType(vo.getBusinessType());
            entity.setFilingNumber(vo.getFilingNumber());
            entity.setClassificationLevel(vo.getClassificationLevel());
            entity.setEvaluationTime(vo.getEvaluationTime());
            entity.setEvaluationResult(vo.getEvaluationResult());
            entity.setEvaluationOrganization(vo.getEvaluationOrganization());
            entity.setEvaluationStatus(vo.getEvaluationStatus());
            entity.setSystemStatus(vo.getSystemStatus());
            entity.setCategory(vo.getCategory());
            entity.setControlPoint(vo.getControlPoint());
            entity.setEvaluationItem(vo.getEvaluationItem());
            entity.setComplianceStatus(vo.getComplianceStatus());
    
            // ------------------- 自动填充plannedEvaluationTime逻辑 -------------------
            String plannedEvaluationTime = vo.getPlannedEvaluationTime();
            String classificationLevel = vo.getClassificationLevel();
            String evaluationTimeStr = vo.getEvaluationTime();
    
            // 当plannedEvaluationTime为空时执行自动填充
            if (plannedEvaluationTime == null || plannedEvaluationTime.trim().isEmpty()) {
                if (classificationLevel != null && evaluationTimeStr != null && !evaluationTimeStr.isEmpty()) {
                    try {
                        // 解析日期（假设格式为yyyy-MM-dd，可根据实际情况调整格式）
                        LocalDate evaluationDate = LocalDate.parse(evaluationTimeStr, DateTimeFormatter.ofPattern("yyyy/MM/dd")); // 调整格式
                        LocalDate plannedDate;
    
                        // 根据等级计算时间
                        if ("三级".equals(classificationLevel)) {
                            plannedDate = evaluationDate.plusMonths(8);
                        } else if ("二级".equals(classificationLevel)) {
                            plannedDate = evaluationDate.plusMonths(20);
                        } else {
                            // 非二级/三级不处理（保持为空）
                            plannedDate = null;
                        }
    
                        // 填充计算后的时间
                        if (plannedDate != null) {
                            entity.setPlannedEvaluationTime(plannedDate.format(DateTimeFormatter.ofPattern("yyyy/MM/dd")));
                        }
                    } catch (DateTimeParseException e) {
                        // 日期格式错误时抛出异常（终止当前数据导入）
                        throw new RuntimeException("evaluationTime格式错误，需为yyyy/MM/dd格式", e);
                    }
                }
            } else {
                // 非空时直接赋值
                entity.setPlannedEvaluationTime(plannedEvaluationTime);
            }
    
            return entity;
        }).collect(Collectors.toList());
    
        // 3. 批量保存数据
        sysProtectService.saveBatch(entityList);
    
        return Result.success("导入成功");
    }

    //批量导出
    @GetMapping("/export")
    @OperationLog(
        moduleName = "等保管理",
        operationType = "导出",
        desc = "导出等保"
    )
    public void exportExcel(HttpServletResponse response,
                            @RequestParam(value = "systemOwnerOrg", required = false) String systemOwnerOrg,
                            @RequestParam(value = "memberUnit", required = false) String memberUnit,
                            @RequestParam(value = "systemName", required = false) String systemName,
                            @RequestParam(value = "systemShortName", required = false) String systemShortName,
                            @RequestParam(value = "networkBelonging", required = false) String networkBelonging,
                            @RequestParam(value = "businessType", required = false) String businessType,
                            @RequestParam(value = "filingNumber", required = false) String filingNumber,
                            @RequestParam(value = "classificationLevel", required = false) String classificationLevel,
                            @RequestParam(value = "evaluationTime", required = false) String evaluationTime,
                            @RequestParam(value = "evaluationResult", required = false) String evaluationResult,
                            @RequestParam(value = "plannedEvaluationTime", required = false) String plannedEvaluationTime,
                            @RequestParam(value = "evaluationOrganization", required = false) String evaluationOrganization,
                            @RequestParam(value = "evaluationStatus", required = false) String evaluationStatus,
                            @RequestParam(value = "systemStatus", required = false) String systemStatus,
                            @RequestParam(value = "category", required = false) String category,
                            @RequestParam(value = "controlPoint", required = false) String controlPoint,
                            @RequestParam(value = "evaluationItem", required = false) String evaluationItem,
                            @RequestParam(value = "complianceStatus", required = false) String complianceStatus
    ) throws IOException {
        // 设置响应格式和文件名
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("系统保护数据", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-Disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        // 构建查询条件（与/list接口相同）
        LambdaQueryWrapper<SysProtect> wrapper = new LambdaQueryWrapper<>();

        if (systemOwnerOrg!= null) {
            wrapper.eq(SysProtect::getSystemOwnerOrg, systemOwnerOrg);
        }
        if (memberUnit != null) {
            wrapper.eq(SysProtect::getMemberUnit, memberUnit);
        }
        if (systemName != null) {
            wrapper.eq(SysProtect::getSystemName, systemName);
        }
        if (systemShortName != null) {
            wrapper.eq(SysProtect::getSystemShortName, systemShortName);
        }
        if (networkBelonging != null) {
            wrapper.eq(SysProtect::getNetworkBelonging, networkBelonging);
        }
        if (businessType != null) {
            wrapper.eq(SysProtect::getBusinessType, businessType);
        }
        if (filingNumber != null) {
            wrapper.eq(SysProtect::getFilingNumber, filingNumber);
        }
        if (classificationLevel != null) {
            wrapper.eq(SysProtect::getClassificationLevel, classificationLevel);
        }
        if (evaluationTime != null) {
            wrapper.eq(SysProtect::getEvaluationTime, evaluationTime);
        }
        if (evaluationResult != null) {
            wrapper.eq(SysProtect::getEvaluationResult, evaluationResult);
        }
        if (plannedEvaluationTime != null) {
            wrapper.eq(SysProtect::getPlannedEvaluationTime, plannedEvaluationTime);
        }
        if (evaluationOrganization != null) {
            wrapper.eq(SysProtect::getEvaluationOrganization, evaluationOrganization);
        }
        if (evaluationStatus != null) {
            wrapper.eq(SysProtect::getEvaluationStatus, evaluationStatus);
        }
        if (systemStatus != null) {
            wrapper.eq(SysProtect::getSystemStatus, systemStatus);
        }
        if (category != null) {
            wrapper.eq(SysProtect::getCategory, category);
        }
        if (controlPoint != null) {
            wrapper.eq(SysProtect::getControlPoint, controlPoint);
        }
        if (evaluationItem != null) {
            wrapper.eq(SysProtect::getEvaluationItem, evaluationItem);
        }
        if (complianceStatus != null) {
            wrapper.eq(SysProtect::getComplianceStatus, complianceStatus);
        }
        // 其他条件...

        // 获取数据并转换
        List<SysProtectExcelVO> voList = sysProtectService.list(wrapper)
                .stream()
                .map(SysProtectExcelVO::new)
                .collect(Collectors.toList());

        // 使用EasyExcel导出
        EasyExcel.write(response.getOutputStream(), SysProtectExcelVO.class)
                .sheet("系统保护数据")
                .doWrite(voList);
    }

}
