<?xml version="1.0" encoding="UTF-8"?>
<dataSource name="management@localhost">
  <database-model serializer="dbm" dbms="MYSQL" family-id="MYSQL" format-version="4.52">
    <root id="1">
      <DefaultCasing>lower/lower</DefaultCasing>
      <DefaultEngine>InnoDB</DefaultEngine>
      <DefaultTmpEngine>InnoDB</DefaultTmpEngine>
      <Grants>|root||root|localhost|ALTER|G
|root||root|localhost|ALTER ROUTINE|G
|root||root|localhost|CREATE|G
|root||root|localhost|CREATE ROUTINE|G
|root||root|localhost|CREATE TABLESPACE|G
|root||root|localhost|CREATE TEMPORARY TABLES|G
|root||root|localhost|CREATE USER|G
|root||root|localhost|CREATE VIEW|G
|root||root|localhost|DELETE|G
|root||root|localhost|DROP|G
|root||root|localhost|EVENT|G
|root||root|localhost|EXECUTE|G
|root||root|localhost|FILE|G
|root||root|localhost|INDEX|G
|root||root|localhost|INSERT|G
|root||root|localhost|LOCK TABLES|G
|root||root|localhost|PROCESS|G
|root||root|localhost|REFERENCES|G
|root||root|localhost|RELOAD|G
|root||root|localhost|REPLICATION CLIENT|G
|root||root|localhost|REPLICATION SLAVE|G
|root||root|localhost|SELECT|G
|root||root|localhost|SHOW DATABASES|G
|root||root|localhost|SHOW VIEW|G
|root||root|localhost|SHUTDOWN|G
|root||root|localhost|SUPER|G
|root||root|localhost|TRIGGER|G
|root||root|localhost|UPDATE|G
|root||root|localhost|grant option|G</Grants>
      <ServerVersion>5.5.13</ServerVersion>
    </root>
    <collation id="2" parent="1" name="big5_chinese_ci">
      <Charset>big5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="3" parent="1" name="big5_bin">
      <Charset>big5</Charset>
    </collation>
    <collation id="4" parent="1" name="dec8_swedish_ci">
      <Charset>dec8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="5" parent="1" name="dec8_bin">
      <Charset>dec8</Charset>
    </collation>
    <collation id="6" parent="1" name="cp850_general_ci">
      <Charset>cp850</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="7" parent="1" name="cp850_bin">
      <Charset>cp850</Charset>
    </collation>
    <collation id="8" parent="1" name="hp8_english_ci">
      <Charset>hp8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="9" parent="1" name="hp8_bin">
      <Charset>hp8</Charset>
    </collation>
    <collation id="10" parent="1" name="koi8r_general_ci">
      <Charset>koi8r</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="11" parent="1" name="koi8r_bin">
      <Charset>koi8r</Charset>
    </collation>
    <collation id="12" parent="1" name="latin1_german1_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="13" parent="1" name="latin1_swedish_ci">
      <Charset>latin1</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="14" parent="1" name="latin1_danish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="15" parent="1" name="latin1_german2_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="16" parent="1" name="latin1_bin">
      <Charset>latin1</Charset>
    </collation>
    <collation id="17" parent="1" name="latin1_general_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="18" parent="1" name="latin1_general_cs">
      <Charset>latin1</Charset>
    </collation>
    <collation id="19" parent="1" name="latin1_spanish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="20" parent="1" name="latin2_czech_cs">
      <Charset>latin2</Charset>
    </collation>
    <collation id="21" parent="1" name="latin2_general_ci">
      <Charset>latin2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="22" parent="1" name="latin2_hungarian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="23" parent="1" name="latin2_croatian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="24" parent="1" name="latin2_bin">
      <Charset>latin2</Charset>
    </collation>
    <collation id="25" parent="1" name="swe7_swedish_ci">
      <Charset>swe7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="26" parent="1" name="swe7_bin">
      <Charset>swe7</Charset>
    </collation>
    <collation id="27" parent="1" name="ascii_general_ci">
      <Charset>ascii</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="28" parent="1" name="ascii_bin">
      <Charset>ascii</Charset>
    </collation>
    <collation id="29" parent="1" name="ujis_japanese_ci">
      <Charset>ujis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="30" parent="1" name="ujis_bin">
      <Charset>ujis</Charset>
    </collation>
    <collation id="31" parent="1" name="sjis_japanese_ci">
      <Charset>sjis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="32" parent="1" name="sjis_bin">
      <Charset>sjis</Charset>
    </collation>
    <collation id="33" parent="1" name="hebrew_general_ci">
      <Charset>hebrew</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="34" parent="1" name="hebrew_bin">
      <Charset>hebrew</Charset>
    </collation>
    <collation id="35" parent="1" name="tis620_thai_ci">
      <Charset>tis620</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="36" parent="1" name="tis620_bin">
      <Charset>tis620</Charset>
    </collation>
    <collation id="37" parent="1" name="euckr_korean_ci">
      <Charset>euckr</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="38" parent="1" name="euckr_bin">
      <Charset>euckr</Charset>
    </collation>
    <collation id="39" parent="1" name="koi8u_general_ci">
      <Charset>koi8u</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="40" parent="1" name="koi8u_bin">
      <Charset>koi8u</Charset>
    </collation>
    <collation id="41" parent="1" name="gb2312_chinese_ci">
      <Charset>gb2312</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="42" parent="1" name="gb2312_bin">
      <Charset>gb2312</Charset>
    </collation>
    <collation id="43" parent="1" name="greek_general_ci">
      <Charset>greek</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="44" parent="1" name="greek_bin">
      <Charset>greek</Charset>
    </collation>
    <collation id="45" parent="1" name="cp1250_general_ci">
      <Charset>cp1250</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="46" parent="1" name="cp1250_czech_cs">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="47" parent="1" name="cp1250_croatian_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="48" parent="1" name="cp1250_bin">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="49" parent="1" name="cp1250_polish_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="50" parent="1" name="gbk_chinese_ci">
      <Charset>gbk</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="51" parent="1" name="gbk_bin">
      <Charset>gbk</Charset>
    </collation>
    <collation id="52" parent="1" name="latin5_turkish_ci">
      <Charset>latin5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="53" parent="1" name="latin5_bin">
      <Charset>latin5</Charset>
    </collation>
    <collation id="54" parent="1" name="armscii8_general_ci">
      <Charset>armscii8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="55" parent="1" name="armscii8_bin">
      <Charset>armscii8</Charset>
    </collation>
    <collation id="56" parent="1" name="utf8_general_ci">
      <Charset>utf8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="57" parent="1" name="utf8_bin">
      <Charset>utf8</Charset>
    </collation>
    <collation id="58" parent="1" name="utf8_unicode_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="59" parent="1" name="utf8_icelandic_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="60" parent="1" name="utf8_latvian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="61" parent="1" name="utf8_romanian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="62" parent="1" name="utf8_slovenian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="63" parent="1" name="utf8_polish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="64" parent="1" name="utf8_estonian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="65" parent="1" name="utf8_spanish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="66" parent="1" name="utf8_swedish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="67" parent="1" name="utf8_turkish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="68" parent="1" name="utf8_czech_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="69" parent="1" name="utf8_danish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="70" parent="1" name="utf8_lithuanian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="71" parent="1" name="utf8_slovak_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="72" parent="1" name="utf8_spanish2_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="73" parent="1" name="utf8_roman_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="74" parent="1" name="utf8_persian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="75" parent="1" name="utf8_esperanto_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="76" parent="1" name="utf8_hungarian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="77" parent="1" name="utf8_sinhala_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="78" parent="1" name="ucs2_general_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="79" parent="1" name="ucs2_bin">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="80" parent="1" name="ucs2_unicode_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="81" parent="1" name="ucs2_icelandic_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="82" parent="1" name="ucs2_latvian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="83" parent="1" name="ucs2_romanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="84" parent="1" name="ucs2_slovenian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="85" parent="1" name="ucs2_polish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="86" parent="1" name="ucs2_estonian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="87" parent="1" name="ucs2_spanish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="88" parent="1" name="ucs2_swedish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="89" parent="1" name="ucs2_turkish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="90" parent="1" name="ucs2_czech_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="91" parent="1" name="ucs2_danish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="92" parent="1" name="ucs2_lithuanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="93" parent="1" name="ucs2_slovak_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="94" parent="1" name="ucs2_spanish2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="95" parent="1" name="ucs2_roman_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="96" parent="1" name="ucs2_persian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="97" parent="1" name="ucs2_esperanto_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="98" parent="1" name="ucs2_hungarian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="99" parent="1" name="ucs2_sinhala_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="100" parent="1" name="cp866_general_ci">
      <Charset>cp866</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="101" parent="1" name="cp866_bin">
      <Charset>cp866</Charset>
    </collation>
    <collation id="102" parent="1" name="keybcs2_general_ci">
      <Charset>keybcs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="103" parent="1" name="keybcs2_bin">
      <Charset>keybcs2</Charset>
    </collation>
    <collation id="104" parent="1" name="macce_general_ci">
      <Charset>macce</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="105" parent="1" name="macce_bin">
      <Charset>macce</Charset>
    </collation>
    <collation id="106" parent="1" name="macroman_general_ci">
      <Charset>macroman</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="107" parent="1" name="macroman_bin">
      <Charset>macroman</Charset>
    </collation>
    <collation id="108" parent="1" name="cp852_general_ci">
      <Charset>cp852</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="109" parent="1" name="cp852_bin">
      <Charset>cp852</Charset>
    </collation>
    <collation id="110" parent="1" name="latin7_estonian_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="111" parent="1" name="latin7_general_ci">
      <Charset>latin7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="112" parent="1" name="latin7_general_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="113" parent="1" name="latin7_bin">
      <Charset>latin7</Charset>
    </collation>
    <collation id="114" parent="1" name="utf8mb4_general_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="115" parent="1" name="utf8mb4_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="116" parent="1" name="utf8mb4_unicode_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="117" parent="1" name="utf8mb4_icelandic_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="118" parent="1" name="utf8mb4_latvian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="119" parent="1" name="utf8mb4_romanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="120" parent="1" name="utf8mb4_slovenian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="121" parent="1" name="utf8mb4_polish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="122" parent="1" name="utf8mb4_estonian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="123" parent="1" name="utf8mb4_spanish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="124" parent="1" name="utf8mb4_swedish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="125" parent="1" name="utf8mb4_turkish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="126" parent="1" name="utf8mb4_czech_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="127" parent="1" name="utf8mb4_danish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="128" parent="1" name="utf8mb4_lithuanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="129" parent="1" name="utf8mb4_slovak_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="130" parent="1" name="utf8mb4_spanish2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="131" parent="1" name="utf8mb4_roman_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="132" parent="1" name="utf8mb4_persian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="133" parent="1" name="utf8mb4_esperanto_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="134" parent="1" name="utf8mb4_hungarian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="135" parent="1" name="utf8mb4_sinhala_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="136" parent="1" name="cp1251_bulgarian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="137" parent="1" name="cp1251_ukrainian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="138" parent="1" name="cp1251_bin">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="139" parent="1" name="cp1251_general_ci">
      <Charset>cp1251</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="140" parent="1" name="cp1251_general_cs">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="141" parent="1" name="utf16_general_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="142" parent="1" name="utf16_bin">
      <Charset>utf16</Charset>
    </collation>
    <collation id="143" parent="1" name="utf16_unicode_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="144" parent="1" name="utf16_icelandic_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="145" parent="1" name="utf16_latvian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="146" parent="1" name="utf16_romanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="147" parent="1" name="utf16_slovenian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="148" parent="1" name="utf16_polish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="149" parent="1" name="utf16_estonian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="150" parent="1" name="utf16_spanish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="151" parent="1" name="utf16_swedish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="152" parent="1" name="utf16_turkish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="153" parent="1" name="utf16_czech_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="154" parent="1" name="utf16_danish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="155" parent="1" name="utf16_lithuanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="156" parent="1" name="utf16_slovak_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="157" parent="1" name="utf16_spanish2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="158" parent="1" name="utf16_roman_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="159" parent="1" name="utf16_persian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="160" parent="1" name="utf16_esperanto_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="161" parent="1" name="utf16_hungarian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="162" parent="1" name="utf16_sinhala_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="163" parent="1" name="cp1256_general_ci">
      <Charset>cp1256</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="164" parent="1" name="cp1256_bin">
      <Charset>cp1256</Charset>
    </collation>
    <collation id="165" parent="1" name="cp1257_lithuanian_ci">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="166" parent="1" name="cp1257_bin">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="167" parent="1" name="cp1257_general_ci">
      <Charset>cp1257</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="168" parent="1" name="utf32_general_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="169" parent="1" name="utf32_bin">
      <Charset>utf32</Charset>
    </collation>
    <collation id="170" parent="1" name="utf32_unicode_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="171" parent="1" name="utf32_icelandic_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="172" parent="1" name="utf32_latvian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="173" parent="1" name="utf32_romanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="174" parent="1" name="utf32_slovenian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="175" parent="1" name="utf32_polish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="176" parent="1" name="utf32_estonian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="177" parent="1" name="utf32_spanish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="178" parent="1" name="utf32_swedish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="179" parent="1" name="utf32_turkish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="180" parent="1" name="utf32_czech_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="181" parent="1" name="utf32_danish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="182" parent="1" name="utf32_lithuanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="183" parent="1" name="utf32_slovak_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="184" parent="1" name="utf32_spanish2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="185" parent="1" name="utf32_roman_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="186" parent="1" name="utf32_persian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="187" parent="1" name="utf32_esperanto_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="188" parent="1" name="utf32_hungarian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="189" parent="1" name="utf32_sinhala_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="190" parent="1" name="binary">
      <Charset>binary</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="191" parent="1" name="geostd8_general_ci">
      <Charset>geostd8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="192" parent="1" name="geostd8_bin">
      <Charset>geostd8</Charset>
    </collation>
    <collation id="193" parent="1" name="cp932_japanese_ci">
      <Charset>cp932</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="194" parent="1" name="cp932_bin">
      <Charset>cp932</Charset>
    </collation>
    <collation id="195" parent="1" name="eucjpms_japanese_ci">
      <Charset>eucjpms</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="196" parent="1" name="eucjpms_bin">
      <Charset>eucjpms</Charset>
    </collation>
    <schema id="197" parent="1" name="information_schema">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="198" parent="1" name="management">
      <Current>1</Current>
      <LastIntrospectionLocalTimestamp>2025-08-22.06:30:15</LastIntrospectionLocalTimestamp>
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="199" parent="1" name="mysql">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="200" parent="1" name="performance_schema">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="201" parent="1" name="test">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <user id="202" parent="1" name="root">
      <Host>localhost</Host>
    </user>
    <table id="203" parent="198" name="role">
      <Comment>系统权限表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
COMPACT</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="204" parent="198" name="task">
      <Comment>任务信息表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="205" parent="198" name="users">
      <Engine>InnoDB</Engine>
      <Options>row_format
COMPACT</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="206" parent="198" name="x_crypto_evaluation">
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="207" parent="198" name="x_dept">
      <Comment>部门</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
COMPACT</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="208" parent="198" name="x_dictionary">
      <Engine>InnoDB</Engine>
      <Options>row_format
COMPACT</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="209" parent="198" name="x_files">
      <Engine>InnoDB</Engine>
      <Options>row_format
COMPACT</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="210" parent="198" name="x_logs">
      <Comment>用于记录管理系统中各种操作的详细日志信息</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="211" parent="198" name="x_menu">
      <Engine>InnoDB</Engine>
      <Options>row_format
COMPACT</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="212" parent="198" name="x_role">
      <Engine>InnoDB</Engine>
      <Options>row_format
COMPACT</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="213" parent="198" name="x_role_menu">
      <Engine>InnoDB</Engine>
      <Options>row_format
COMPACT</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="214" parent="198" name="x_sys_protect">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="215" parent="198" name="x_tasks">
      <Comment>任务表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="216" parent="198" name="x_user">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="217" parent="198" name="x_user_role">
      <Engine>InnoDB</Engine>
      <Options>row_format
COMPACT</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="218" parent="198" name="x_vul">
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <column id="219" parent="203" name="role_id">
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="220" parent="203" name="name">
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="221" parent="203" name="level">
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <index id="222" parent="203" name="Role_unique">
      <ColNames>role_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="223" parent="203" name="Role_unique">
      <UnderlyingIndexName>Role_unique</UnderlyingIndexName>
    </key>
    <column id="224" parent="204" name="task_id">
      <AutoIncrement>6</AutoIncrement>
      <Comment>任务ID</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="225" parent="204" name="title">
      <Comment>任务标题</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="226" parent="204" name="publisher">
      <Comment>发布者</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="227" parent="204" name="due_date">
      <Comment>截止日期</Comment>
      <DasType>date|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="228" parent="204" name="importance">
      <Comment>重要性(1-低,2-中,3-高)</Comment>
      <DasType>tinyint(4)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="229" parent="204" name="summary">
      <Comment>任务摘要</Comment>
      <DasType>text|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="230" parent="204" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="231" parent="204" name="status">
      <Comment>任务状态</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="232" parent="204" name="deleted">
      <Comment>删除标记(0-未删除,1-已删除)</Comment>
      <DasType>tinyint(4)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>9</Position>
    </column>
    <index id="233" parent="204" name="PRIMARY">
      <ColNames>task_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="234" parent="204" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="235" parent="205" name="ID">
      <AutoIncrement>4</AutoIncrement>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="236" parent="205" name="username">
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="237" parent="205" name="password">
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="238" parent="205" name="email">
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="239" parent="205" name="permission">
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="240" parent="205" name="organization">
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="241" parent="205" name="time">
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="242" parent="205" name="reviewer">
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="243" parent="205" name="status">
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <index id="244" parent="205" name="PRIMARY">
      <ColNames>ID</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="245" parent="205" name="email">
      <ColNames>email</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="246" parent="205" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="247" parent="205" name="email">
      <UnderlyingIndexName>email</UnderlyingIndexName>
    </key>
    <column id="248" parent="206" name="id">
      <DasType>int(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="249" parent="206" name="system_owner_org">
      <DasType>varchar(100)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="250" parent="206" name="system_name">
      <DasType>varchar(200)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="251" parent="206" name="business_type">
      <DasType>varchar(50)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="252" parent="206" name="filing_number">
      <DasType>varchar(50)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="253" parent="206" name="classification_level">
      <DasType>varchar(50)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="254" parent="206" name="evaluation_time">
      <DasType>date|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="255" parent="206" name="evaluation_organization">
      <DasType>varchar(100)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="256" parent="206" name="evaluation_status">
      <DasType>varchar(20)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="257" parent="206" name="system_status">
      <DasType>varchar(20)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="258" parent="206" name="category">
      <DasType>varchar(50)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="259" parent="206" name="control_point">
      <DasType>varchar(100)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="260" parent="206" name="evaluation_item">
      <DasType>varchar(200)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="261" parent="206" name="result_record">
      <DasType>text|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="262" parent="206" name="risk_level">
      <DasType>varchar(10)|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="263" parent="206" name="rectification_suggestion">
      <DasType>text|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="264" parent="206" name="crypto_score">
      <DasType>decimal(5,2 digit)|0s</DasType>
      <Position>17</Position>
    </column>
    <column id="265" parent="206" name="estimated_score_after_fix">
      <DasType>decimal(5,2 digit)|0s</DasType>
      <Position>18</Position>
    </column>
    <column id="266" parent="206" name="evaluation_result">
      <DasType>varchar(20)|0s</DasType>
      <Position>19</Position>
    </column>
    <index id="267" parent="206" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="268" parent="206" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="269" parent="207" name="dept_id">
      <AutoIncrement>20</AutoIncrement>
      <Comment>ID</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="270" parent="207" name="pid">
      <Comment>上级部门</Comment>
      <DasType>int(11)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="271" parent="207" name="name">
      <Comment>名称</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="272" parent="207" name="status">
      <Comment>状态</Comment>
      <DasType>varchar(1)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="273" parent="207" name="phone">
      <Comment>联系电话</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="274" parent="207" name="address">
      <Comment>地址</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="275" parent="207" name="deleted">
      <Comment>删除标签</Comment>
      <DasType>int(11)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <index id="276" parent="207" name="PRIMARY">
      <ColNames>dept_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="277" parent="207" name="inx_pid">
      <ColNames>pid</ColNames>
      <Type>btree</Type>
    </index>
    <index id="278" parent="207" name="inx_enabled">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <key id="279" parent="207" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="280" parent="208" name="diction_id">
      <AutoIncrement>3</AutoIncrement>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="281" parent="208" name="term">
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="282" parent="208" name="form">
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="283" parent="208" name="descibe">
      <DasType>varchar(255)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="284" parent="208" name="delate">
      <Comment>是否删除</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <index id="285" parent="208" name="PRIMARY">
      <ColNames>diction_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="286" parent="208" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="287" parent="209" name="file_id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="288" parent="209" name="task_id">
      <DasType>int(11)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="289" parent="209" name="file_name">
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="290" parent="209" name="file_type">
      <DasType>varchar(50)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="291" parent="209" name="file_path">
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="292" parent="209" name="upload_time">
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="293" parent="209" name="deleted">
      <Comment>删除标签</Comment>
      <DasType>int(11)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <foreign-key id="294" parent="209" name="x_files_ibfk_1">
      <ColNames>task_id</ColNames>
      <RefColNames>task_id</RefColNames>
      <RefTableName>task</RefTableName>
    </foreign-key>
    <index id="295" parent="209" name="PRIMARY">
      <ColNames>file_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="296" parent="209" name="task_id">
      <ColNames>task_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="297" parent="209" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="298" parent="210" name="id">
      <AutoIncrement>377</AutoIncrement>
      <Comment>操作日志的唯一标识，采用自增方式生成，用于唯一区分每条日志记录</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="299" parent="210" name="username">
      <Comment>执行操作的用户的用户名，可用于追溯操作人</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="300" parent="210" name="realname">
      <Comment>执行操作的用户的真实姓名，辅助识别用户身份</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="301" parent="210" name="module_name">
      <Comment>操作所属的系统模块，例如用户管理、商品管理等，便于对操作进行分类统计和分析</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="302" parent="210" name="operation_type">
      <Comment>具体的操作类型，如登录、创建、删除、更新等，描述操作的性质</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="303" parent="210" name="operation_description">
      <Comment>对操作的详细描述，记录操作的具体内容和相关参数，帮助理解操作细节</Comment>
      <DasType>text|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="304" parent="210" name="operation_time">
      <Comment>操作发起的时间，精确到秒，记录操作发生的具体时刻</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="305" parent="210" name="operation_ip">
      <Comment>操作发起时的 IP 地址，用于安全审计和追踪异常操作</Comment>
      <DasType>varchar(45)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="306" parent="210" name="device_info">
      <Comment>操作所使用的设备信息，包括浏览器类型、操作系统等，有助于了解操作环境</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="307" parent="210" name="related_data_id">
      <Comment>若操作涉及具体的数据记录，此为关联数据的 ID，方便进行关联查询</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="308" parent="210" name="operation_result">
      <Comment>操作的结果，如成功、失败等，可根据实际情况自定义结果状态</Comment>
      <DasType>varchar(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <column id="309" parent="210" name="error_message">
      <Comment>当操作失败时，记录具体的错误信息，便于排查问题</Comment>
      <DasType>text|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="310" parent="210" name="is_sensitive">
      <Comment>标记该操作是否为敏感操作，可用于特殊监控和处理</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>13</Position>
    </column>
    <index id="311" parent="210" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="312" parent="210" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="313" parent="211" name="menu_id">
      <AutoIncrement>5</AutoIncrement>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="314" parent="211" name="component">
      <DasType>varchar(100)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="315" parent="211" name="path">
      <DasType>varchar(100)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="316" parent="211" name="redirect">
      <DasType>varchar(100)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="317" parent="211" name="name">
      <DasType>varchar(100)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="318" parent="211" name="title">
      <DasType>varchar(100)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="319" parent="211" name="icon">
      <DasType>varchar(100)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="320" parent="211" name="parent_id">
      <DasType>int(11)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="321" parent="211" name="is_leaf">
      <DasType>varchar(1)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="322" parent="211" name="hidden">
      <DasType>tinyint(1)|0s</DasType>
      <Position>10</Position>
    </column>
    <index id="323" parent="211" name="PRIMARY">
      <ColNames>menu_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="324" parent="211" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="325" parent="212" name="role_id">
      <AutoIncrement>4</AutoIncrement>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="326" parent="212" name="role_name">
      <DasType>varchar(50)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="327" parent="212" name="role_desc">
      <DasType>varchar(100)|0s</DasType>
      <Position>3</Position>
    </column>
    <index id="328" parent="212" name="PRIMARY">
      <ColNames>role_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="329" parent="212" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="330" parent="213" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="331" parent="213" name="role_id">
      <DasType>int(11)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="332" parent="213" name="menu_id">
      <DasType>int(11)|0s</DasType>
      <Position>3</Position>
    </column>
    <index id="333" parent="213" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="334" parent="213" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="335" parent="214" name="application_id">
      <AutoIncrement>24</AutoIncrement>
      <DasType>int(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="336" parent="214" name="system_owner_org">
      <DasType>varchar(100)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="337" parent="214" name="member_unit">
      <DasType>varchar(100)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="338" parent="214" name="system_name">
      <DasType>varchar(200)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="339" parent="214" name="system_short_name">
      <DasType>varchar(50)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="340" parent="214" name="network_belonging">
      <DasType>varchar(50)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="341" parent="214" name="business_type">
      <DasType>varchar(50)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="342" parent="214" name="filing_number">
      <DasType>varchar(100)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="343" parent="214" name="classification_level">
      <DasType>varchar(20)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="344" parent="214" name="evaluation_time">
      <DasType>date|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="345" parent="214" name="evaluation_result">
      <DasType>varchar(50)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="346" parent="214" name="planned_evaluation_time">
      <DasType>date|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="347" parent="214" name="evaluation_organization">
      <DasType>varchar(100)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="348" parent="214" name="evaluation_status">
      <DasType>varchar(50)|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="349" parent="214" name="system_status">
      <DasType>varchar(50)|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="350" parent="214" name="category">
      <DasType>varchar(50)|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="351" parent="214" name="control_point">
      <DasType>varchar(50)|0s</DasType>
      <Position>17</Position>
    </column>
    <column id="352" parent="214" name="evaluation_item">
      <DasType>varchar(200)|0s</DasType>
      <Position>18</Position>
    </column>
    <column id="353" parent="214" name="compliance_status">
      <DasType>varchar(50)|0s</DasType>
      <Position>19</Position>
    </column>
    <column id="354" parent="214" name="result_record">
      <DasType>varchar(200)|0s</DasType>
      <Position>20</Position>
    </column>
    <index id="355" parent="214" name="PRIMARY">
      <ColNames>application_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="356" parent="214" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="357" parent="215" name="task_id">
      <AutoIncrement>6</AutoIncrement>
      <Comment>任务唯一主键（自增）</Comment>
      <DasType>int(11) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="358" parent="215" name="task_status">
      <Comment>任务状态（待处理/已完成）</Comment>
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;未开始&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </column>
    <column id="359" parent="215" name="create_time">
      <Comment>任务创建时间（自动记录）</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="360" parent="215" name="end_time">
      <Comment>任务结束时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="361" parent="215" name="responsible_person">
      <Comment>任务负责人</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>5</Position>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </column>
    <column id="362" parent="215" name="deleted">
      <Comment>软删除标记（0=未删除，1=已删除）</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="363" parent="215" name="system_owner_org">
      <DasType>varchar(100)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="364" parent="215" name="system_name">
      <DasType>varchar(200)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="365" parent="215" name="system_status">
      <DasType>varchar(50)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="366" parent="215" name="task_type">
      <DasType>varchar(45)|0s</DasType>
      <Position>10</Position>
    </column>
    <index id="367" parent="215" name="PRIMARY">
      <ColNames>task_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="368" parent="215" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="369" parent="216" name="id">
      <AutoIncrement>18</AutoIncrement>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="370" parent="216" name="username">
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="371" parent="216" name="password">
      <DasType>varchar(100)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="372" parent="216" name="email">
      <DasType>varchar(50)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="373" parent="216" name="phone">
      <DasType>varchar(20)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="374" parent="216" name="status">
      <DasType>int(1)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="375" parent="216" name="avatar">
      <DasType>varchar(200)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="376" parent="216" name="role">
      <DasType>varchar(10)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="377" parent="216" name="organization">
      <Comment>所属单位</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="378" parent="216" name="reviewer">
      <Comment>用户审核人</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="379" parent="216" name="deleted">
      <Comment>删除标签</Comment>
      <DasType>int(11)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <column id="380" parent="216" name="realname">
      <Comment>用户的姓名</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>12</Position>
    </column>
    <index id="381" parent="216" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="382" parent="216" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="383" parent="217" name="id">
      <AutoIncrement>2</AutoIncrement>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="384" parent="217" name="user_id">
      <DasType>int(11)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="385" parent="217" name="role_id">
      <DasType>int(11)|0s</DasType>
      <Position>3</Position>
    </column>
    <index id="386" parent="217" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="387" parent="217" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="388" parent="218" name="id">
      <DasType>int(45)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="389" parent="218" name="system_owner_org">
      <DasType>varchar(100)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="390" parent="218" name="system_name">
      <DasType>varchar(100)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="391" parent="218" name="business_type">
      <DasType>varchar(50)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="392" parent="218" name="vul_id">
      <DasType>varchar(45)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="393" parent="218" name="vul_name">
      <DasType>varchar(45)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="394" parent="218" name="vul_type">
      <DasType>varchar(45)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="395" parent="218" name="disclosure_date">
      <DasType>date|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="396" parent="218" name="vul_level">
      <DasType>varchar(10)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="397" parent="218" name="vul_status">
      <DasType>varchar(10)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="398" parent="218" name="vul_description">
      <DasType>text|0s</DasType>
      <Position>11</Position>
    </column>
    <index id="399" parent="218" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="400" parent="218" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
  </database-model>
</dataSource>