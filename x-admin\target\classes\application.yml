server:
  port: 9999

spring:
  datasource:
    username: root
    password: root
    url: *********************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
  redis:
    port: 6379
    host: localhost
  mail:
    host: smtp.163.com
    username: <EMAIL>
    password: JRWY9rS3K5PuuiTR
    protocol: smtp
  task:
    scheduling:
      enabled: true # 启用定时任务

logging:
  level:
    com.example: debug
    org.springframework.mail: DEBUG  # 关键！显示 SMTP 连接、认证、发送等日志

mybatis-plus:
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 开启上传和下载
spring.servlet.multipart.enabled: true
# 上传文件大小限制
spring.servlet.multipart.max-file-size: 20MB
#单词最大请求大小限制
spring.servlet.multipart.max-request-size: 20MB

# 配置文件上传路径
file.upload.dir: d:/upload/

#自定义禁止上传的文件类型
file:
  forbiddenFileTypes:
    - .exe
    - .bat
    - .sh
    - .js
    - .php
    - .asp
    - .jsp
    - .html
    - .htm
    - .py
    - .rb
    - .pl

# 新增：控制项目启动时是否发送邮件（默认 true，设为 false 则禁止启动发送）
reminder:
  send-on-start: true  # 关键配置项
  cron: 0 0 9 * * ?  # 每天早上九点执行一次
